import { Input } from "@/components/ui/input";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, HelpCircle } from "lucide-react";
import { 
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PhoneNumberSearchProps {
  phoneNumber: string;
  setPhoneNumber: (value: string) => void;
  validationError: string;
  setValidationError: (value: string) => void;
}

export const PhoneNumberSearch = ({
  phoneNumber,
  setPhoneNumber,
  validationError,
  setValidationError,
}: PhoneNumberSearchProps) => {
  const [isFocused, setIsFocused] = useState(false);

  const validatePhoneNumber = (phone: string): boolean => {
    if (!phone.trim()) return false;
    
    // Remove all non-digits for validation
    const digitsOnly = phone.replace(/\D/g, '');
    
    // Require at least 6 digits for a meaningful search
    return digitsOnly.length >= 6;
  };

  const formatPhoneNumber = (phone: string): string => {
    // Remove all non-digits
    const digitsOnly = phone.replace(/\D/g, '');
    
    // Format US numbers as (XXX) XXX-XXXX
    if (digitsOnly.length === 10) {
      return `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`;
    }
    
    // Format US numbers with country code as +1 (XXX) XXX-XXXX
    if (digitsOnly.length === 11 && digitsOnly.startsWith('1')) {
      return `+1 (${digitsOnly.slice(1, 4)}) ${digitsOnly.slice(4, 7)}-${digitsOnly.slice(7)}`;
    }
    
    // For other formats, just return the original input
    return phone;
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label htmlFor="phoneNumber" className="text-sm font-medium flex items-center">
          Phone Number
          <span className="text-red-500 ml-1">*</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" className="h-5 w-5 p-0 ml-1">
                  <HelpCircle size={14} />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>Enter a phone number to search for prospects. We'll search across all phone number fields in our database.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </label>
        <div className="relative">
          <Input
            id="phoneNumber"
            type="tel"
            placeholder="(************* or ******-123-4567"
            value={phoneNumber}
            onChange={(e) => {
              const newValue = e.target.value;
              setPhoneNumber(newValue);
              
              if (validatePhoneNumber(newValue)) {
                setValidationError("");
              } else if (newValue.trim()) {
                setValidationError("Please enter at least 6 digits for a valid phone number search");
              }
            }}
            onFocus={() => setIsFocused(true)}
            onBlur={() => {
              setIsFocused(false);
              // Format phone number on blur
              if (phoneNumber) {
                setPhoneNumber(formatPhoneNumber(phoneNumber));
              }
            }}
            className={`w-full ${validationError && phoneNumber ? 'border-red-500' : ''}`}
          />
          
          {validationError && phoneNumber && !isFocused && (
            <div className="absolute right-2 top-2.5 text-red-500">
              <AlertCircle size={16} />
            </div>
          )}
        </div>
        
        <div className="text-xs">
          <p className="text-gray-500">Phone Number Formats We Accept:</p>
          <ul className="ml-4 list-disc text-gray-400">
            <li>(*************</li>
            <li>************</li>
            <li>******-123-4567</li>
            <li>5551234567</li>
            <li>International formats</li>
          </ul>
        </div>
      </div>
      
      {validationError && phoneNumber && !isFocused && (
        <div className="text-sm text-red-500 flex items-center">
          <AlertCircle size={14} className="mr-1" /> 
          {validationError}
        </div>
      )}
    </div>
  );
};
